/**
 * 综合JavaScript测试文件
 * 包含各种JavaScript功能和示例代码
 * 作者: Kwaipilot
 * 日期: 2025年6月18日
 */

// ========== 基础数据类型和变量 ==========
console.log('=== 基础数据类型和变量 ===');

// 基本数据类型
const stringExample = 'Hello, World!';
const numberExample = 42;
const booleanExample = true;
const nullExample = null;
const undefinedExample = undefined;
const symbolExample = Symbol('test');
const bigintExample = 123456789012345678901234567890n;

console.log('字符串:', stringExample);
console.log('数字:', numberExample);
console.log('布尔值:', booleanExample);
console.log('空值:', nullExample);
console.log('未定义:', undefinedExample);
console.log('符号:', symbolExample);
console.log('大整数:', bigintExample);

// ========== 对象和数组操作 ==========
console.log('\n=== 对象和数组操作 ===');

// 对象示例
const person = {
    name: '张三',
    age: 30,
    city: '北京',
    hobbies: ['阅读', '游泳', '编程'],
    address: {
        street: '长安街',
        zipCode: '100000'
    }
};

console.log('人员信息:', person);
console.log('姓名:', person.name);
console.log('爱好:', person.hobbies.join(', '));

// 数组操作
const numbers = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10];
const fruits = ['苹果', '香蕉', '橙子', '葡萄', '草莓'];

// 数组方法示例
const evenNumbers = numbers.filter(num => num % 2 === 0);
const doubledNumbers = numbers.map(num => num * 2);
const sum = numbers.reduce((acc, num) => acc + num, 0);

console.log('原数组:', numbers);
console.log('偶数:', evenNumbers);
console.log('翻倍后:', doubledNumbers);
console.log('总和:', sum);

// ========== 函数定义和使用 ==========
console.log('\n=== 函数定义和使用 ===');

// 普通函数
function calculateArea(length, width) {
    return length * width;
}

// 箭头函数
const calculateCircleArea = (radius) => Math.PI * radius * radius;

// 高阶函数
function createMultiplier(factor) {
    return function(number) {
        return number * factor;
    };
}

// 使用函数
console.log('矩形面积 (5x3):', calculateArea(5, 3));
console.log('圆形面积 (半径=5):', calculateCircleArea(5).toFixed(2));

const triple = createMultiplier(3);
console.log('3的倍数:', triple(7));

// ========== 类和面向对象编程 ==========
console.log('\n=== 类和面向对象编程 ===');

class Animal {
    constructor(name, species) {
        this.name = name;
        this.species = species;
    }

    speak() {
        console.log(`${this.name} 发出了声音`);
    }

    getInfo() {
        return `${this.name} 是一只 ${this.species}`;
    }
}

class Dog extends Animal {
    constructor(name, breed) {
        super(name, '狗');
        this.breed = breed;
    }

    speak() {
        console.log(`${this.name} 汪汪叫`);
    }

    wagTail() {
        console.log(`${this.name} 摇尾巴`);
    }
}

class Cat extends Animal {
    constructor(name, color) {
        super(name, '猫');
        this.color = color;
    }

    speak() {
        console.log(`${this.name} 喵喵叫`);
    }

    purr() {
        console.log(`${this.name} 发出呼噜声`);
    }
}

// 创建实例
const dog = new Dog('旺财', '金毛');
const cat = new Cat('小白', '白色');

console.log(dog.getInfo());
dog.speak();
dog.wagTail();

console.log(cat.getInfo());
cat.speak();
cat.purr();

// ========== 异步编程 ==========
console.log('\n=== 异步编程 ===');

// Promise 示例
function fetchData(url) {
    return new Promise((resolve, reject) => {
        setTimeout(() => {
            if (url) {
                resolve(`从 ${url} 获取的数据`);
            } else {
                reject(new Error('URL 不能为空'));
            }
        }, 1000);
    });
}

// 使用 Promise
fetchData('https://api.example.com/data')
    .then(data => console.log('Promise结果:', data))
    .catch(error => console.error('Promise错误:', error));

// async/await 示例
async function asyncDataFetch() {
    try {
        const result = await fetchData('https://api.test.com/users');
        console.log('Async/Await结果:', result);
    } catch (error) {
        console.error('Async/Await错误:', error);
    }
}

asyncDataFetch();

// ========== 字符串处理 ==========
console.log('\n=== 字符串处理 ===');

const sampleText = '  JavaScript 是一门强大的编程语言  ';

// 字符串方法
console.log('原字符串:', `"${sampleText}"`);
console.log('去除空格:', `"${sampleText.trim()}"`);
console.log('转大写:', sampleText.toUpperCase());
console.log('转小写:', sampleText.toLowerCase());
console.log('替换:', sampleText.replace('JavaScript', 'JS'));
console.log('分割:', sampleText.trim().split(' '));

// 模板字符串
const name = '李四';
const age = 25;
const template = `你好，我是${name}，今年${age}岁。`;
console.log('模板字符串:', template);

// ========== 正则表达式 ==========
console.log('\n=== 正则表达式 ===');

const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
const phonePattern = /^1[3-9]\d{9}$/;

const testEmails = ['<EMAIL>', 'invalid-email', '<EMAIL>'];
const testPhones = ['13812345678', '12345678901', '18888888888'];

testEmails.forEach(email => {
    console.log(`${email} ${emailPattern.test(email) ? '是' : '不是'} 有效邮箱`);
});

testPhones.forEach(phone => {
    console.log(`${phone} ${phonePattern.test(phone) ? '是' : '不是'} 有效手机号`);
});

// ========== 错误处理 ==========
console.log('\n=== 错误处理 ===');

function divide(a, b) {
    if (b === 0) {
        throw new Error('除数不能为零');
    }
    return a / b;
}

try {
    console.log('10 / 2 =', divide(10, 2));
    console.log('10 / 0 =', divide(10, 0));
} catch (error) {
    console.error('捕获到错误:', error.message);
} finally {
    console.log('除法操作完成');
}

// ========== 模块化示例 ==========
console.log('\n=== 模块化示例 ===');

// 工具函数模块
const MathUtils = {
    add: (a, b) => a + b,
    subtract: (a, b) => a - b,
    multiply: (a, b) => a * b,
    divide: (a, b) => b !== 0 ? a / b : null,
    
    factorial: function(n) {
        if (n <= 1) return 1;
        return n * this.factorial(n - 1);
    },
    
    fibonacci: function(n) {
        if (n <= 1) return n;
        return this.fibonacci(n - 1) + this.fibonacci(n - 2);
    },
    
    isPrime: function(n) {
        if (n <= 1) return false;
        if (n <= 3) return true;
        if (n % 2 === 0 || n % 3 === 0) return false;
        
        for (let i = 5; i * i <= n; i += 6) {
            if (n % i === 0 || n % (i + 2) === 0) return false;
        }
        return true;
    }
};

console.log('加法 5 + 3 =', MathUtils.add(5, 3));
console.log('阶乘 5! =', MathUtils.factorial(5));
console.log('斐波那契数列第10项:', MathUtils.fibonacci(10));
console.log('17是质数吗?', MathUtils.isPrime(17));

// ========== 数据结构实现 ==========
console.log('\n=== 数据结构实现 ===');

// 栈实现
class Stack {
    constructor() {
        this.items = [];
    }
    
    push(item) {
        this.items.push(item);
    }
    
    pop() {
        return this.items.pop();
    }
    
    peek() {
        return this.items[this.items.length - 1];
    }
    
    isEmpty() {
        return this.items.length === 0;
    }
    
    size() {
        return this.items.length;
    }
}

// 队列实现
class Queue {
    constructor() {
        this.items = [];
    }
    
    enqueue(item) {
        this.items.push(item);
    }
    
    dequeue() {
        return this.items.shift();
    }
    
    front() {
        return this.items[0];
    }
    
    isEmpty() {
        return this.items.length === 0;
    }
    
    size() {
        return this.items.length;
    }
}

// 使用栈
const stack = new Stack();
stack.push(1);
stack.push(2);
stack.push(3);
console.log('栈顶元素:', stack.peek());
console.log('弹出元素:', stack.pop());
console.log('栈大小:', stack.size());

// 使用队列
const queue = new Queue();
queue.enqueue('第一个');
queue.enqueue('第二个');
queue.enqueue('第三个');
console.log('队首元素:', queue.front());
console.log('出队元素:', queue.dequeue());
console.log('队列大小:', queue.size());

// ========== DOM 操作模拟 ==========
console.log('\n=== DOM 操作模拟 ===');

// 模拟 DOM 元素
class MockElement {
    constructor(tagName) {
        this.tagName = tagName;
        this.attributes = new Map();
        this.children = [];
        this.textContent = '';
        this.style = {};
    }
    
    setAttribute(name, value) {
        this.attributes.set(name, value);
    }
    
    getAttribute(name) {
        return this.attributes.get(name);
    }
    
    appendChild(child) {
        this.children.push(child);
    }
    
    getElementById(id) {
        if (this.getAttribute('id') === id) {
            return this;
        }
        for (let child of this.children) {
            const result = child.getElementById(id);
            if (result) return result;
        }
        return null;
    }
    
    toString() {
        return `<${this.tagName} ${Array.from(this.attributes.entries()).map(([k, v]) => `${k}="${v}"`).join(' ')}>${this.textContent}</${this.tagName}>`;
    }
}

// 创建模拟 DOM
const document = new MockElement('html');
const body = new MockElement('body');
const div = new MockElement('div');
div.setAttribute('id', 'main');
div.setAttribute('class', 'container');
div.textContent = '这是主要内容';

body.appendChild(div);
document.appendChild(body);

console.log('模拟DOM结构:', document.toString());
console.log('查找元素:', document.getElementById('main').toString());

// ========== 事件系统模拟 ==========
console.log('\n=== 事件系统模拟 ===');

class EventEmitter {
    constructor() {
        this.events = {};
    }
    
    on(event, listener) {
        if (!this.events[event]) {
            this.events[event] = [];
        }
        this.events[event].push(listener);
    }
    
    emit(event, ...args) {
        if (this.events[event]) {
            this.events[event].forEach(listener => listener(...args));
        }
    }
    
    off(event, listener) {
        if (this.events[event]) {
            this.events[event] = this.events[event].filter(l => l !== listener);
        }
    }
}

const emitter = new EventEmitter();

// 注册事件监听器
emitter.on('user-login', (username) => {
    console.log(`用户 ${username} 已登录`);
});

emitter.on('user-logout', (username) => {
    console.log(`用户 ${username} 已登出`);
});

// 触发事件
emitter.emit('user-login', '张三');
emitter.emit('user-logout', '张三');

// ========== 工具函数集合 ==========
console.log('\n=== 工具函数集合 ===');

const Utils = {
    // 深拷贝
    deepClone: function(obj) {
        if (obj === null || typeof obj !== 'object') return obj;
        if (obj instanceof Date) return new Date(obj);
        if (obj instanceof Array) return obj.map(item => this.deepClone(item));
        if (typeof obj === 'object') {
            const clonedObj = {};
            for (let key in obj) {
                if (obj.hasOwnProperty(key)) {
                    clonedObj[key] = this.deepClone(obj[key]);
                }
            }
            return clonedObj;
        }
    },
    
    // 防抖函数
    debounce: function(func, delay) {
        let timeoutId;
        return function(...args) {
            clearTimeout(timeoutId);
            timeoutId = setTimeout(() => func.apply(this, args), delay);
        };
    },
    
    // 节流函数
    throttle: function(func, limit) {
        let inThrottle;
        return function(...args) {
            if (!inThrottle) {
                func.apply(this, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    },
    
    // 随机数生成
    randomBetween: function(min, max) {
        return Math.floor(Math.random() * (max - min + 1)) + min;
    },
    
    // 数组去重
    unique: function(array) {
        return [...new Set(array)];
    },
    
    // 格式化日期
    formatDate: function(date, format = 'YYYY-MM-DD') {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        
        return format
            .replace('YYYY', year)
            .replace('MM', month)
            .replace('DD', day);
    }
};

// 测试工具函数
const originalObj = { name: '测试', data: { count: 5 } };
const clonedObj = Utils.deepClone(originalObj);
clonedObj.data.count = 10;

console.log('原对象:', originalObj);
console.log('克隆对象:', clonedObj);

const testArray = [1, 2, 2, 3, 3, 3, 4, 5];
console.log('原数组:', testArray);
console.log('去重后:', Utils.unique(testArray));

console.log('随机数:', Utils.randomBetween(1, 100));
console.log('格式化日期:', Utils.formatDate(new Date()));

// ========== 算法示例 ==========
console.log('\n=== 算法示例 ===');

// 排序算法
const SortAlgorithms = {
    // 冒泡排序
    bubbleSort: function(arr) {
        const result = [...arr];
        for (let i = 0; i < result.length - 1; i++) {
            for (let j = 0; j < result.length - i - 1; j++) {
                if (result[j] > result[j + 1]) {
                    [result[j], result[j + 1]] = [result[j + 1], result[j]];
                }
            }
        }
        return result;
    },
    
    // 快速排序
    quickSort: function(arr) {
        if (arr.length <= 1) return arr;
        
        const pivot = arr[Math.floor(arr.length / 2)];
        const left = arr.filter(x => x < pivot);
        const middle = arr.filter(x => x === pivot);
        const right = arr.filter(x => x > pivot);
        
        return [
            ...this.quickSort(left),
            ...middle,
            ...this.quickSort(right)
        ];
    },
    
    // 二分查找
    binarySearch: function(arr, target) {
        let left = 0;
        let right = arr.length - 1;
        
        while (left <= right) {
            const mid = Math.floor((left + right) / 2);
            if (arr[mid] === target) return mid;
            if (arr[mid] < target) left = mid + 1;
            else right = mid - 1;
        }
        return -1;
    }
};

const unsortedArray = [64, 34, 25, 12, 22, 11, 90];
console.log('原数组:', unsortedArray);
console.log('冒泡排序:', SortAlgorithms.bubbleSort(unsortedArray));
console.log('快速排序:', SortAlgorithms.quickSort(unsortedArray));

const sortedArray = [1, 3, 5, 7, 9, 11, 13, 15];
console.log('在已排序数组中查找 7:', SortAlgorithms.binarySearch(sortedArray, 7));

// ========== 设计模式示例 ==========
console.log('\n=== 设计模式示例 ===');

// 单例模式
class Singleton {
    constructor() {
        if (Singleton.instance) {
            return Singleton.instance;
        }
        this.data = {};
        Singleton.instance = this;
    }
    
    setData(key, value) {
        this.data[key] = value;
    }
    
    getData(key) {
        return this.data[key];
    }
}

// 观察者模式
class Observer {
    constructor(name) {
        this.name = name;
    }
    
    update(message) {
        console.log(`${this.name} 收到消息: ${message}`);
    }
}

class Subject {
    constructor() {
        this.observers = [];
    }
    
    addObserver(observer) {
        this.observers.push(observer);
    }
    
    removeObserver(observer) {
        this.observers = this.observers.filter(obs => obs !== observer);
    }
    
    notifyObservers(message) {
        this.observers.forEach(observer => observer.update(message));
    }
}

// 测试设计模式
const singleton1 = new Singleton();
const singleton2 = new Singleton();
singleton1.setData('test', 'Hello World');
console.log('单例模式测试:', singleton2.getData('test'));

const subject = new Subject();
const observer1 = new Observer('观察者1');
const observer2 = new Observer('观察者2');
console.log('\n=== 测试文件结束 ===');
console.log('这个文件包含了 JavaScript 的各种功能和示例');
console.log('总计超过 500 行代码，涵盖了：');
console.log('- 基础语法和数据类型');
console.log('- 函数和面向对象编程');
console.log('- 异步编程和 Promise');
console.log('- 数据结构实现');
console.log('- 算法示例');
console.log('- 设计模式');
console.log('- 工具函数');
console.log('感谢使用！');

subject.addObserver(observer1);
subject.addObserver(observer2);
subject.notifyObservers('这是一条测试消息');

// ========== 结束语 ==========
console.log('\n=== 测试文件结束 ===');
console.log('这个文件包含了 JavaScript 的各种功能和示例');
console.log('总计超过 500 行代码，涵盖了：');
console.log('- 基础语法和数据类型');
console.log('- 函数和面向对象编程');
console.log('- 异步编程和 Promise');
console.log('- 数据结构实现');
console.log('- 算法示例');
console.log('- 设计模式');
console.log('- 工具函数');
console.log('感谢使用！');
